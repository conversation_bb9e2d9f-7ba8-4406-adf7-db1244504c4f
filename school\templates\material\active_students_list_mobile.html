{% load humanize %}
{% load static %}

<div class="content-header" id="content-header">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span>Gestion des élèves</span>
    </h2>
    <div class="actions">
        <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
            <span class="mdc-button__label">Ajouter un élève</span>
        </button>
        <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn"
              onclick="console.log('Search icon clicked!'); if(typeof openMobileSearch === 'function') openMobileSearch(); else console.error('openMobileSearch not found');">search</span>
        <span class="material-icons" title="Filtrer" id="filter-btn">filter_list</span>
    </div>
</div>

<div class="content-area" id="content-area">
    <!-- Quick Filter Chips -->
    <div class="quick-filter-chips" hx-target="#content-area">
        <button class="quick-filter-chip {% if not 'filter_by' in request.GET or not request.GET.filter_by or request.GET.filter_by == 'all' %} active {% endif %}"
                hx-get="{{ request.path }}?filter_by=all" hx-push-url="true"
                onclick="createRipple(event, this)">
            <span class="material-icons">group</span>
            <span>Tous</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'active' %} active {% endif %}"
                hx-get="{{ request.path }}?filter_by=active" hx-push-url="true"
                onclick="createRipple(event, this)">
            <span class="material-icons">check_circle</span>
            <span>Actifs</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'inactive' %} active {% endif %}"
                hx-get="{{ request.path }}?filter_by=inactive" hx-push-url="true"
                onclick="createRipple(event, this)">
            <span class="material-icons">cancel</span>
            <span>Inactifs</span>
        </button>
    </div>

    <!-- Students List (Mobile) -->
    <div class="students-list">
        {% for enrollment in enrollments %}
            <div class="student-item" data-student-id="{{ enrollment.id }}">
                <div class="student-photo" style="background-image: url('{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}')"></div>
                <div class="student-info">
                    <div class="student-header">
                        <div class="student-name">{{ enrollment.student.get_full_name }}</div>
                        <div class="student-id">{{ enrollment.student.student_id|default:"-" }}</div>
                    </div>
                    {% if enrollment.student.full_name_ar %}
                        <div class="student-name-ar">{{ enrollment.student.full_name_ar }}</div>
                    {% endif %}
                    <div class="student-birth-date">
                        {% if enrollment.student.birth_place %}{{ enrollment.student.birth_place }}{% endif %}
                        {% if enrollment.student.birth_place and enrollment.student.gender %} • {% endif %}
                        {{ enrollment.student.get_gender_display }}
                    </div>
                    <div class="student-grades">
                        {% if enrollment.level_fr %}
                            <span class="grade-fr">{{ enrollment.level_fr }}</span>
                        {% endif %}
                        {% if enrollment.level_fr and enrollment.level_ar %}
                            <span class="grade-separator">•</span>
                        {% endif %}
                        {% if enrollment.level_ar %}
                            <span class="grade-ar">{{ enrollment.level_ar }}</span>
                        {% endif %}
                    </div>
                    <div class="student-status">
                        <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                            {{ enrollment.status|default:'Actif' }}
                        </span>
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn" title="Modifier l'élève" 
                            hx-get="{% url 'school:student_edit' enrollment.pk %}" 
                            hx-target="#dialog">
                        <span class="material-icons">edit</span>
                    </button>
                    <button class="view-btn" title="Voir détails"
                            hx-get="{% url 'school:student_detail' enrollment.pk %}" 
                            hx-target="#app-content">
                        <span class="material-icons">visibility</span>
                    </button>
                </div>
            </div>
        {% empty %}
            <!-- Empty State -->
            <div class="empty-state">
                <span class="material-icons">search_off</span>
                <div class="empty-text">Aucun élève trouvé</div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    <div class="pagination-container">
        <div class="pagination-info">
            <span>Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}</span>
            <span>({{ page_obj.paginator.count }} élèves au total)</span>
        </div>
        <div class="pagination-controls">
            {% if page_obj.has_previous %}
                <button class="pagination-btn" 
                        hx-get="{{ request.path }}?page={{ page_obj.previous_page_number }}"
                        hx-target="#content-area">
                    <span class="material-icons">chevron_left</span>
                </button>
            {% else %}
                <button class="pagination-btn" disabled>
                    <span class="material-icons">chevron_left</span>
                </button>
            {% endif %}
            
            {% if page_obj.has_next %}
                <button class="pagination-btn" 
                        hx-get="{{ request.path }}?page={{ page_obj.next_page_number }}"
                        hx-target="#content-area">
                    <span class="material-icons">chevron_right</span>
                </button>
            {% else %}
                <button class="pagination-btn" disabled>
                    <span class="material-icons">chevron_right</span>
                </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- QR Code Floating Action Button -->
<button class="mdc-fab qr-fab" id="qr-fab" title="Scanner QR Code">
    <div class="mdc-fab__ripple"></div>
    <span class="material-icons mdc-fab__icon">qr_code</span>
</button>

<!-- Add Student Floating Action Button -->
<button class="mdc-fab mdc-fab--extended" id="add-student-fab" title="Ajouter un élève">
    <div class="mdc-fab__ripple"></div>
    <span class="material-icons mdc-fab__icon">add</span>
    <span class="mdc-fab__label">Ajouter un élève</span>
</button>

<!-- <script src="{% static 'material/js/table-row-selection.js' %}"></script>
<script src="{% static 'material/js/floating-actions.js' %}"></script> -->

<script>
// Initialize mobile search when this template loads
document.addEventListener('DOMContentLoaded', function() {
    // Ensure mobile search is initialized
    if (typeof initializeMobileSearch === 'function') {
        initializeMobileSearch();
    }
});

// Also initialize when HTMX loads this content
document.body.addEventListener('htmx:afterSwap', function(event) {
    // Check if this swap included the mobile search button
    if (event.detail.target.querySelector && event.detail.target.querySelector('#mobile-search-btn')) {
        if (typeof initializeMobileSearch === 'function') {
            initializeMobileSearch();
        }
    }
});

// Immediate initialization for current page
if (typeof initializeMobileSearch === 'function') {
    initializeMobileSearch();
}
</script>