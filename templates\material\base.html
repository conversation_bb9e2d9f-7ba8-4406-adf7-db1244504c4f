{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="description" content="EcolePro est une application web progressive de gestion des écoles laïques et islamiques">
    <meta name="keywords" content="EcolePro, application, gestion, école, moyennes">
    <!-- Open Graph Meta-->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="EcolePro: Application de Gestion des Ecoles">
    <meta property="og:title" content="EcolePro: Gestion des Ecoles">
    <meta property="og:url" content="https://www.ecolepro.net/">
    <meta property="og:image" content="{% static 'img/favicon.png' %}">
    <meta property="og:description" content="EcolePro est une application de gestion des écoles en Côte d'Ivoire">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro | Gestion des écoles</title>
    
    <link rel="shortcut icon" href="{% static 'img/favicon.png' %}" type="image/x-icon">
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/material/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/material/matter.css' %}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>
<body x-data="minimalistApp()" hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'>
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Loading...</div>
        </div>
    </div>

    <!-- Loading Overlay for UI Interactions -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="mdc-circular-progress mdc-circular-progress--medium" role="progressbar" aria-label="Processing..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text" id="loading-text">Processing...</div>
        </div>
    </div>

    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1 x-text="pageTitle">Dashboard</h1>
        <div class="actions">
            <span class="material-icons" id="dark-mode-toggle" title="Toggle Dark Mode"  @click="if(navigator.vibrate) navigator.vibrate(10);">dark_mode</span>
            <span class="material-icons" id="more-menu-btn">more_vert</span>
        </div>
    </div>
    
    {% include 'material/sidebar.html' %}
    
    <!-- Main Content -->
    <div class="main-content" id="main-content" hx-target="this">
        {% include template_name|default:'material/main.html' %}
    </div>

    <!-- Bottom App Bar (Mobile) -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-nav-item" @click="setActiveNav('home')" :class="{ 'active': activeNav === 'home' }">
            <span class="material-icons">home</span>
            <span class="bottom-nav-label">Accueil</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('page1')" :class="{ 'active': activeNav === 'page1' }">
            <span class="material-icons">people</span>
            <span class="bottom-nav-label">Page 1</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('section1')" :class="{ 'active': activeNav === 'section1' }">
            <span class="material-icons">request_quote</span>
            <span class="bottom-nav-label">Section 1</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('section2')" :class="{ 'active': activeNav === 'section2' }">
            <span class="material-icons">leaderboard</span>
            <span class="bottom-nav-label">Section 2</span>
        </div>
    </div>

    {% include 'material/user_offcanvas.html' %}

    <!-- Sidebar Overlay for Mobile -->
    <div class="overlay" id="sidebar-overlay"></div>

    <!-- Modal Component -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal" id="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Modal Title</h3>
                <button class="modal-close" id="modal-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content" id="modal-content">
                <p>Modal content goes here...</p>
            </div>
            <div class="modal-actions" id="modal-actions">
                <button class="mdc-button" id="modal-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button class="mdc-button mdc-button--raised" id="modal-confirm">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Confirm</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Overlay -->
    <div class="mobile-search-overlay" id="mobile-search-overlay">
        <div class="mobile-search-container">
            <div class="mobile-search-header">
                <button class="mobile-search-back" id="mobile-search-back">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="mobile-search-input-container">
                    <input type="text" class="mobile-search-input" id="mobile-search-input"
                           placeholder="Rechercher des élèves..." autocomplete="off">
                </div>
            </div>
        </div>
        <div class="mobile-search-results" id="mobile-search-results">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <!-- Snackbar Component -->
    <div class="mdc-snackbar" id="snackbar">
        <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
            <div class="mdc-snackbar__label" id="snackbar-label" aria-atomic="false">
                Message goes here
            </div>
            <div class="mdc-snackbar__actions" id="snackbar-actions">
                <button type="button" class="mdc-button mdc-snackbar__action" id="snackbar-action">
                    <div class="mdc-button__ripple"></div>
                    <span class="mdc-button__label">Action</span>
                </button>
                <button class="mdc-icon-button mdc-snackbar__dismiss material-icons" title="Dismiss" id="snackbar-dismiss">close</button>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="{% static 'js/htmx.min.js' %}"></script>
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Flatpickr -->
    
    <!-- Custom JavaScript -->
    <script src="{% static 'js/material/scripts.js' %}"></script>
    <script src="{% static 'js/material/charts.js' %}"></script>
    <script src="{% static 'material/js/modal-htmx.js' %}"></script>
    <script src="{% static 'material/js/mdc-auto-init.js' %}"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/fr.js" defer></script>
</body>
</html>
