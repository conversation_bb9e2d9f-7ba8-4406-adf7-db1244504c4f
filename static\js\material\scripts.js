// ===================================
// ALPINE.JS DATA STORE
// ===================================

function minimalistApp() {
    return {
        // State variables
        activeNav: 'home',
        pageTitle: 'Dashboard',

        // Methods
        setActiveNav(nav) {
            this.activeNav = nav;
            this.updatePageTitle(nav);

            // Close sidebar on mobile after navigation
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        },

        updatePageTitle(nav) {
            const titles = {
                'home': 'Dashboard',
                'page1': 'Page 1',
                'section1': 'Section 1',
                'section2': 'Section 2',
                'subsection1': 'Subsection 1',
                'subsection2': 'Subsection 2',
                'subsection3': 'Subsection 3',
                'subsection4': 'Subsection 4',
                'souspartie1': 'Sous-partie 1',
                'souspartie2': 'Sous-partie 2'
            };

            this.pageTitle = titles[nav] || 'Dashboard';
        }
    }
}

// ===================================
// MATERIAL DESIGN COMPONENTS INITIALIZATION
// ===================================

// Initialize Material Design Components
mdc.autoInit();

// Initialize page preloader spinner
let pagePreloaderSpinner;
if (window.mdc && window.mdc.circularProgress) {
    const preloaderElement = document.getElementById('page-preloader-spinner');
    if (preloaderElement) {
        pagePreloaderSpinner = new mdc.circularProgress.MDCCircularProgress(preloaderElement);
    }
}

// ===================================
// LOADING FUNCTIONS
// ===================================

function showPagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.remove('hidden');
    
    // Start the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.open();
    }
}

function hidePagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.add('hidden');
    
    // Stop the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.close();
    }
    
    // Remove preloader from DOM after animation completes
    setTimeout(() => {
        preloader.style.display = 'none';
    }, 500);
}

function showLoadingOverlay(text = 'Processing...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = document.getElementById('loading-text');
    loadingText.textContent = text;
    overlay.classList.add('active');
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    overlay.classList.remove('active');
}

// ===================================
// SIDEBAR FUNCTIONS
// ===================================

function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const contentHeader = document.querySelector('.content-header');

    if (window.innerWidth <= 768) {
        // Mobile behavior: toggle open class
        if (sidebar) {
            sidebar.classList.toggle('open');
            const overlay = document.getElementById('sidebar-overlay');
            if (sidebar.classList.contains('open')) {
                overlay.classList.add('active');
                document.body.style.overflow = 'hidden';
                // Change hamburger to close icon
                updateMenuIcon(true);
            } else {
                overlay.classList.remove('active');
                document.body.style.overflow = '';
                // Change close icon back to hamburger
                updateMenuIcon(false);
            }
        }
    } else {
        // Desktop behavior: toggle collapsed class
        if (sidebar) {
            sidebar.classList.toggle('collapsed');
            // Update menu icon based on sidebar state
            updateMenuIcon(!sidebar.classList.contains('collapsed'));
        }
        if (mainContent) {
            mainContent.classList.toggle('expanded');
        }
        if (contentHeader) {
            contentHeader.classList.toggle('expanded');
        }
    }
}

// Function to update menu icon
function updateMenuIcon(isOpen) {
    const menuBtn = document.getElementById('menu-btn');
    if (!menuBtn) return;

    if (isOpen) {
        // Show close icon
        menuBtn.classList.add('menu-active');
        menuBtn.innerHTML = 'close';
    } else {
        // Show hamburger menu icon
        menuBtn.classList.remove('menu-active');
        menuBtn.innerHTML = 'menu';
    }
}

function openSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    sidebar.classList.add('open');
    overlay.classList.add('active');
    document.body.style.overflow = 'hidden';
    // Show close icon
    updateMenuIcon(true);
}

function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    sidebar.classList.remove('open');
    overlay.classList.remove('active');
    document.body.style.overflow = '';
    // Reset menu icon to hamburger
    updateMenuIcon(false);
}

// ===================================
// OFFCANVAS FUNCTIONS
// ===================================

function toggleUserOffcanvas() {
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');

    if (offcanvasOverlay.classList.contains('active')) {
        closeUserOffcanvas();
    } else {
        openUserOffcanvas();
    }
}

function openUserOffcanvas() {
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');
    const offcanvas = document.getElementById('user-offcanvas');

    offcanvasOverlay.classList.add('active');
    offcanvas.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeUserOffcanvas() {
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');
    const offcanvas = document.getElementById('user-offcanvas');

    offcanvasOverlay.classList.remove('active');
    offcanvas.classList.remove('active');
    document.body.style.overflow = '';
}

// ===================================
// DARK MODE FUNCTIONS
// ===================================

// Dark Mode State
let isDarkMode = localStorage.getItem('darkMode') === 'true';

function toggleDarkMode() {
    isDarkMode = !isDarkMode;
    localStorage.setItem('darkMode', isDarkMode);
    updateDarkModeUI();
}

function updateDarkModeUI() {
    const darkModeToggle = document.getElementById('dark-mode-toggle');

    if (isDarkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
        if (darkModeToggle) {
            darkModeToggle.textContent = 'light_mode';
            darkModeToggle.title = 'Switch to Light Mode';
        }
    } else {
        document.documentElement.removeAttribute('data-theme');
        if (darkModeToggle) {
            darkModeToggle.textContent = 'dark_mode';
            darkModeToggle.title = 'Switch to Dark Mode';
        }
    }
}

// ===================================
// MODAL FUNCTIONS
// ===================================

let modalInstance = null;

function showModal(options = {}) {
    const {
        title = 'Modal Title',
        content = 'Modal content goes here...',
        confirmText = 'Confirm',
        cancelText = 'Cancel',
        showCancel = true,
        onConfirm = null,
        onCancel = null,
        onClose = null
    } = options;

    const modalOverlay = document.getElementById('modal-overlay');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    const modalConfirm = document.getElementById('modal-confirm');
    const modalCancel = document.getElementById('modal-cancel');

    // Set content
    modalTitle.textContent = title;
    modalContent.innerHTML = content;
    modalConfirm.querySelector('.mdc-button__label').textContent = confirmText;
    modalCancel.querySelector('.mdc-button__label').textContent = cancelText;

    // Show/hide cancel button
    modalCancel.style.display = showCancel ? 'inline-flex' : 'none';

    // Store callbacks
    modalInstance = {
        onConfirm,
        onCancel,
        onClose
    };

    // Show modal
    modalOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus on confirm button
    setTimeout(() => {
        modalConfirm.focus();
    }, 100);
}

function hideModal() {
    const modalOverlay = document.getElementById('modal-overlay');
    modalOverlay.classList.remove('active');
    document.body.style.overflow = '';

    // Call onClose callback if provided
    if (modalInstance && modalInstance.onClose) {
        modalInstance.onClose();
    }

    modalInstance = null;
}

// ===================================
// SNACKBAR FUNCTIONS
// ===================================

let snackbarMDC = null;

function showSnackbar(message, options = {}) {
    const {
        actionText = null,
        onAction = null,
        timeout = 4000,
        dismissible = true
    } = options;

    const snackbar = document.getElementById('snackbar');
    const snackbarLabel = document.getElementById('snackbar-label');
    const snackbarAction = document.getElementById('snackbar-action');
    const snackbarDismiss = document.getElementById('snackbar-dismiss');

    // Initialize MDC Snackbar if not already done
    if (!snackbarMDC && window.mdc && window.mdc.snackbar) {
        snackbarMDC = new mdc.snackbar.MDCSnackbar(snackbar);
    }

    // Set message
    snackbarLabel.textContent = message;

    // Configure action button
    if (actionText && onAction) {
        snackbarAction.querySelector('.mdc-button__label').textContent = actionText;
        snackbarAction.style.display = 'inline-flex';
        snackbarAction.onclick = () => {
            onAction();
            if (snackbarMDC) {
                snackbarMDC.close();
            }
        };
    } else {
        snackbarAction.style.display = 'none';
    }

    // Configure dismiss button
    snackbarDismiss.style.display = dismissible ? 'inline-flex' : 'none';

    // Set timeout
    if (snackbarMDC) {
        snackbarMDC.timeoutMs = timeout;
        snackbarMDC.open();
    } else {
        // Fallback if MDC is not available
        snackbar.classList.add('mdc-snackbar--open');
        setTimeout(() => {
            snackbar.classList.remove('mdc-snackbar--open');
        }, timeout);
    }
}

// ===================================
// SCROLL BEHAVIOR FUNCTIONS
// ===================================

function initializeScrollBehavior() {
    let lastScrollTop = 0;
    let ticking = false;

    // Get elements
    const topAppBar = document.querySelector('.app-bar');
    const bottomAppBar = document.getElementById('bottom-app-bar');
    const contentHeader = document.querySelector('.content-header');

    console.log('Initializing scroll behavior...', {
        topAppBar: !!topAppBar,
        bottomAppBar: !!bottomAppBar,
        contentHeader: !!contentHeader,
        windowWidth: window.innerWidth,
        isMobile: window.innerWidth <= 768
    });

    if (!topAppBar && !bottomAppBar) {
        console.log('No app bars found, skipping scroll behavior');
        return;
    }

    // Force initial state
    if (topAppBar) {
        topAppBar.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        topAppBar.style.transform = 'translateY(0)';
    }

    if (bottomAppBar) {
        bottomAppBar.style.transition = 'transform 0.3s ease';
        bottomAppBar.style.transform = 'translateY(0)';
    }

    if (contentHeader) {
        contentHeader.style.transition = 'top 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        contentHeader.style.top = '56px';
    }

    function handleScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                const scrollDelta = Math.abs(currentScrollTop - lastScrollTop);

                // Only react to significant scroll movements (more than 5px)
                if (scrollDelta > 5) {
                    if (currentScrollTop > lastScrollTop && currentScrollTop > 50) {
                        // Scrolling down - hide app bars
                        hideScrollElements();
                    } else if (currentScrollTop < lastScrollTop) {
                        // Scrolling up - show app bars
                        showScrollElements();
                    }

                    lastScrollTop = Math.max(0, currentScrollTop); // Prevent negative values
                }

                ticking = false;
            });

            ticking = true;
        }
    }

    function hideScrollElements() {
        // Hide bottom app bar (mobile only)
        var sideBar = document.getElementById('sidebar');
        var sideBarCollapsed = !sideBar.classList.contains('open');

        if (bottomAppBar && window.innerWidth <= 768 && sideBarCollapsed) {
            bottomAppBar.style.transform = 'translateY(100%)';
        }

        // Hide top app bar (on all devices)
        if (topAppBar && sideBarCollapsed) {
            topAppBar.style.transform = 'translateY(-100%)';

            // Adjust content header position when app bar is hidden
            if (contentHeader) {
                contentHeader.style.top = '0';
            }
        }
    }

    function showScrollElements() {
        // Show bottom app bar (mobile only)
        if (bottomAppBar && window.innerWidth <= 768) {
            bottomAppBar.style.transform = 'translateY(0)';
        }

        // Show top app bar (on all devices)
        if (topAppBar) {
            topAppBar.style.transform = 'translateY(0)';

            // Restore content header position when app bar is shown
            if (contentHeader) {
                contentHeader.style.top = '56px';
            }
        }
    }

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Touch support for mobile
    let touchStartY = 0;
    let touchEndY = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartY = e.changedTouches[0].screenY;
    }, { passive: true });

    document.addEventListener('touchend', (e) => {
        touchEndY = e.changedTouches[0].screenY;
        const touchDelta = Math.abs(touchEndY - touchStartY);

        if (touchDelta > 50) { // Significant touch movement
            if (touchEndY < touchStartY) {
                // Swiping up (scrolling down)
                hideScrollElements();
            } else {
                // Swiping down (scrolling up)
                showScrollElements();
            }
        }
    }, { passive: true });

    // Handle window resize
    window.addEventListener('resize', () => {
        // Reset elements on resize
        if (topAppBar) {
            topAppBar.style.transform = 'translateY(0)';
        }
        if (bottomAppBar) {
            bottomAppBar.style.transform = 'translateY(0)';
        }
        if (contentHeader) {
            contentHeader.style.top = '56px';
        }
    });

    // Store cleanup function
    window.cleanupScrollBehavior = () => {
        window.removeEventListener('scroll', handleScroll);
    };

    // Manual hide/show functions for testing
    window.hideElements = hideScrollElements;
    window.showElements = showScrollElements;

    // Show scroll elements when content header back button is clicked
    const contentHeaderBackBtn = document.querySelector('.content-header .material-icons:first-child');
    if (contentHeaderBackBtn) {
        contentHeaderBackBtn.addEventListener('click', () => {
            showScrollElements();
        });
    }
}

// ===================================
// SUBMENU FUNCTIONS
// ===================================

function toggleSubmenu(submenuId) {
    const submenu = document.getElementById(submenuId);
    const parentItem = document.querySelector(`[data-submenu="${submenuId}"]`);

    if (submenu && parentItem) {
        const isExpanded = submenu.classList.contains('expanded');

        // Close all other submenus at the same level
        document.querySelectorAll('.submenu.expanded').forEach(menu => {
            if (menu !== submenu && !menu.closest('.submenu-level-2')) {
                menu.classList.remove('expanded');
                const parent = document.querySelector(`[data-submenu="${menu.id}"]`);
                if (parent) {
                    parent.classList.remove('expanded');
                }
            }
        });

        // Toggle current submenu
        if (isExpanded) {
            submenu.classList.remove('expanded');
            parentItem.classList.remove('expanded');
        } else {
            submenu.classList.add('expanded');
            parentItem.classList.add('expanded');
        }
    }
}

// Function to handle level-2 submenu toggles
function toggleLevel2Submenu(submenuId) {
    const submenu = document.getElementById(submenuId);
    const parentItem = document.querySelector(`[data-submenu="${submenuId}"]`);

    if (submenu && parentItem) {
        const isExpanded = submenu.classList.contains('expanded');

        // Close all other level-2 submenus
        document.querySelectorAll('.submenu-level-2.expanded').forEach(menu => {
            if (menu !== submenu) {
                menu.classList.remove('expanded');
                const parent = document.querySelector(`[data-submenu="${menu.id}"]`);
                if (parent) {
                    parent.classList.remove('expanded');
                }
            }
        });

        // Toggle current level-2 submenu
        if (isExpanded) {
            submenu.classList.remove('expanded');
            parentItem.classList.remove('expanded');
        } else {
            submenu.classList.add('expanded');
            parentItem.classList.add('expanded');
        }
    }
}

// ===================================
// EVENT LISTENERS
// ===================================

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dark mode
    if (isDarkMode) {
        document.documentElement.setAttribute('data-theme', 'dark');
    }
    updateDarkModeUI();
    
    // Menu button click handler
    const menuBtn = document.getElementById('menu-btn');
    if (menuBtn) {
        menuBtn.addEventListener('click', toggleSidebar);
        // Initialize menu icon state
        updateMenuIcon(false);
    }
    
    // Dark mode toggle
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', toggleDarkMode);
    }
    
    // More menu button (user offcanvas)
    const moreMenuBtn = document.getElementById('more-menu-btn');
    if (moreMenuBtn) {
        moreMenuBtn.addEventListener('click', toggleUserOffcanvas);
    }
    
    // Close user offcanvas button
    const closeUserOffcanvasBtn = document.getElementById('close-user-offcanvas');
    if (closeUserOffcanvasBtn) {
        closeUserOffcanvasBtn.addEventListener('click', closeUserOffcanvas);
    }
    
    // Sidebar overlay click handler
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            closeSidebar();
        });
    }

    // Offcanvas overlay click handler
    const offcanvasOverlay = document.getElementById('offcanvas-overlay');
    if (offcanvasOverlay) {
        offcanvasOverlay.addEventListener('click', function(e) {
            if (e.target === offcanvasOverlay) {
                closeUserOffcanvas();
            }
        });
    }
    
    // Submenu toggle handlers for main sidebar items
    document.querySelectorAll('.sidebar-item-with-submenu').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const submenuId = this.dataset.submenu;
            if (submenuId) {
                toggleSubmenu(submenuId);
            }
        });
    });

    // Submenu toggle handlers for level-2 submenu items
    document.querySelectorAll('.submenu-item-with-submenu').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const submenuId = this.dataset.submenu;
            if (submenuId) {
                toggleLevel2Submenu(submenuId);
            }
        });
    });
    
    // Initialize modal event listeners
    const modalOverlay = document.getElementById('modal-overlay');
    const modalClose = document.getElementById('modal-close');
    const modalConfirm = document.getElementById('modal-confirm');
    const modalCancel = document.getElementById('modal-cancel');

    if (modalClose) {
        modalClose.addEventListener('click', () => {
            if (modalInstance && modalInstance.onClose) {
                modalInstance.onClose();
            }
            hideModal();
        });
    }

    if (modalConfirm) {
        modalConfirm.addEventListener('click', () => {
            if (modalInstance && modalInstance.onConfirm) {
                modalInstance.onConfirm();
            }
            hideModal();
        });
    }

    if (modalCancel) {
        modalCancel.addEventListener('click', () => {
            if (modalInstance && modalInstance.onCancel) {
                modalInstance.onCancel();
            }
            hideModal();
        });
    }

    if (modalOverlay) {
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                if (modalInstance && modalInstance.onClose) {
                    modalInstance.onClose();
                }
                hideModal();
            }
        });
    }

    // Initialize snackbar
    const snackbar = document.getElementById('snackbar');
    if (snackbar && window.mdc && window.mdc.snackbar) {
        snackbarMDC = new mdc.snackbar.MDCSnackbar(snackbar);
    }

    // Add ripple effects to offcanvas menu items
    document.querySelectorAll('.offcanvas-menu-item').forEach(item => {
        item.addEventListener('click', (e) => {
            createRipple(e, item, true);
        });
    });

    // Keyboard support for modal, offcanvas and sidebar
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const modalOverlay = document.getElementById('modal-overlay');
            const offcanvasOverlay = document.getElementById('offcanvas-overlay');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            if (modalOverlay && modalOverlay.classList.contains('active')) {
                if (modalInstance && modalInstance.onClose) {
                    modalInstance.onClose();
                }
                hideModal();
            } else if (offcanvasOverlay && offcanvasOverlay.classList.contains('active')) {
                closeUserOffcanvas();
            } else if (sidebarOverlay && sidebarOverlay.classList.contains('active')) {
                closeSidebar();
            }
        }
    });

    // Initialize scroll behavior (with delay to ensure DOM is ready)
    setTimeout(() => {
        initializeScrollBehavior();
    }, 500);

    // Initialize sidebar search
    initializeSidebarSearch();

    // Hide page preloader after page load
    setTimeout(() => {
        hidePagePreloader();
    }, 1000);
});

// ===================================
// RESPONSIVE HANDLERS
// ===================================

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const contentHeader = document.querySelector('.content-header');

    if (window.innerWidth > 768) {
        // Desktop: remove mobile classes and close offcanvas
        if (sidebar) {
            sidebar.classList.remove('open');
            // Update menu icon based on collapsed state
            updateMenuIcon(!sidebar.classList.contains('collapsed'));
        }
        closeSidebar();
        closeUserOffcanvas();
    } else {
        // Mobile: remove desktop classes
        if (sidebar) {
            sidebar.classList.remove('collapsed');
        }
        if (mainContent) {
            mainContent.classList.remove('expanded');
        }
        if (contentHeader) {
            contentHeader.classList.remove('expanded');
        }
        // Reset menu icon to hamburger for mobile
        updateMenuIcon(false);
    }
});

// ===================================
// DEMO FUNCTIONS
// ===================================

function showModalDemo() {
    showModal({
        title: 'Demo Modal',
        content: '<p>This is a demo modal with Material Design styling.</p><p>You can customize the title, content, and button labels.</p>',
        confirmText: 'Got it',
        cancelText: 'Cancel',
        showCancel: true,
        onConfirm: () => {
            showSnackbar('Modal confirmed!');
        },
        onCancel: () => {
            showSnackbar('Modal cancelled');
        },
        onClose: () => {
            console.log('Modal closed');
        }
    });
}

function showSnackbarDemo() {
    showSnackbar('This is a simple snackbar message!');
}

function showSnackbarWithAction() {
    showSnackbar('Message with action button', {
        actionText: 'UNDO',
        onAction: () => {
            showSnackbar('Action button clicked!');
        },
        timeout: 6000
    });
}

// ===================================
// SIDEBAR SEARCH FUNCTIONS
// ===================================

let originalSidebarState = null;

function initializeSidebarSearch() {
    const searchInput = document.getElementById('sidebar-search-input');
    if (!searchInput) return;

    // Store original state of all sidebar items
    storeOriginalSidebarState();

    // Add event listener for search input
    searchInput.addEventListener('input', handleSidebarSearch);
}

function storeOriginalSidebarState() {
    originalSidebarState = {
        sidebarItems: [],
        submenus: [],
        submenuItems: []
    };

    // Store all sidebar items
    document.querySelectorAll('.sidebar-item').forEach(item => {
        originalSidebarState.sidebarItems.push({
            element: item,
            display: getComputedStyle(item).display,
            text: item.textContent.trim()
        });
    });

    // Store all submenus
    document.querySelectorAll('.submenu').forEach(submenu => {
        originalSidebarState.submenus.push({
            element: submenu,
            display: getComputedStyle(submenu).display,
            expanded: submenu.classList.contains('expanded'),
            searchExpanded: submenu.classList.contains('search-expanded')
        });
    });

    // Store all submenu items
    document.querySelectorAll('.submenu-item').forEach(item => {
        originalSidebarState.submenuItems.push({
            element: item,
            display: getComputedStyle(item).display,
            text: item.textContent.trim()
        });
    });
}

function handleSidebarSearch(event) {
    const query = event.target.value.toLowerCase().trim();

    if (query === '') {
        resetSidebarToOriginalState();
        return;
    }

    filterSidebarItems(query);
}

function resetSidebarToOriginalState() {
    if (!originalSidebarState) return;

    // Reset all sidebar items
    originalSidebarState.sidebarItems.forEach(item => {
        item.element.style.display = item.display;
    });

    // Reset all submenus
    originalSidebarState.submenus.forEach(submenu => {
        submenu.element.style.display = submenu.display;
        submenu.element.classList.remove('search-expanded');
        if (!submenu.expanded) {
            submenu.element.classList.remove('expanded');
        }
    });

    // Reset all submenu items
    originalSidebarState.submenuItems.forEach(item => {
        item.element.style.display = item.display;
    });
}

function filterSidebarItems(query) {
    if (!originalSidebarState) return;

    // Hide all items initially
    originalSidebarState.sidebarItems.forEach(item => {
        item.element.style.display = 'none';
    });
    originalSidebarState.submenus.forEach(submenu => {
        submenu.element.style.display = 'none';
        submenu.element.classList.remove('search-expanded');
    });
    originalSidebarState.submenuItems.forEach(item => {
        item.element.style.display = 'none';
    });

    // Find matching items and show them with their parents
    const matchingItems = new Set();
    const menusToShow = new Set();
    const submenusToExpand = new Set();

    // Check sidebar items
    originalSidebarState.sidebarItems.forEach(item => {
        if (item.text.toLowerCase().includes(query)) {
            matchingItems.add(item.element);

            // If this item has a submenu, mark it for expansion
            const submenuId = item.element.dataset.submenu;
            if (submenuId) {
                submenusToExpand.add(submenuId);
            }
        }
    });

    // Check submenu items (including level-2 submenu items)
    originalSidebarState.submenuItems.forEach(item => {
        if (item.text.toLowerCase().includes(query)) {
            matchingItems.add(item.element);

            // Find all parent elements up the hierarchy
            let currentElement = item.element;

            // Traverse up to find all parent submenus and sidebar items
            while (currentElement) {
                const parentSubmenu = currentElement.closest('.submenu');
                if (parentSubmenu) {
                    menusToShow.add(parentSubmenu);
                    submenusToExpand.add(parentSubmenu.id);

                    // Check if this submenu is a level-2 submenu
                    if (parentSubmenu.classList.contains('submenu-level-2')) {
                        // Find the parent submenu-item that controls this level-2 submenu
                        const parentSubmenuItem = document.querySelector(`[data-submenu="${parentSubmenu.id}"]`);
                        if (parentSubmenuItem) {
                            matchingItems.add(parentSubmenuItem);

                            // Find the parent level-1 submenu
                            const level1Submenu = parentSubmenuItem.closest('.submenu');
                            if (level1Submenu) {
                                menusToShow.add(level1Submenu);
                                submenusToExpand.add(level1Submenu.id);
                            }
                        }
                    }

                    // Find and show parent sidebar item
                    const parentSidebarItem = document.querySelector(`[data-submenu="${parentSubmenu.id}"]`);
                    if (parentSidebarItem && parentSidebarItem.classList.contains('sidebar-item')) {
                        matchingItems.add(parentSidebarItem);
                    }

                    // Move up to check for more parent levels
                    currentElement = parentSubmenu.parentElement;
                } else {
                    break;
                }
            }
        }
    });

    // Show matching sidebar items
    matchingItems.forEach(item => {
        item.style.display = '';
    });

    // Show and expand relevant submenus
    menusToShow.forEach(submenu => {
        submenu.style.display = '';
        submenu.classList.add('search-expanded');
    });

    // Expand submenus that have matching items
    submenusToExpand.forEach(submenuId => {
        const submenu = document.getElementById(submenuId);
        if (submenu) {
            submenu.style.display = '';
            submenu.classList.add('search-expanded');
        }
    });

    // Show matching submenu items
    originalSidebarState.submenuItems.forEach(item => {
        if (item.text.toLowerCase().includes(query)) {
            item.element.style.display = '';
        }
    });
}

// ===================================
// SWEETALERT UTILITY FUNCTIONS
// ===================================

// Success alert for general actions (status 204)
function showSuccessAlert(title = 'Fait', text = 'Action éffectuée avec succès') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'success',
        confirmButtonText: 'OK',
        timer: 3000,
        timerProgressBar: true
    });
}

// Payment success alert with receipt options (status 206)
function showPaymentSuccessAlert(receiptUrl) {
    return Swal.fire({
        title: "Enregistré!",
        html: "Enregistré avec succès. Voulez-vous imprimer un reçu de paiement? Vous pouvez choisir entre deux modèles: <br> <strong> * Modèle 1 (par Rubrique : inscription, scolarité, etc)</strong> <br><strong> * Modèle 2 (qui affiche la liste de tous les paiements)</strong>.",
        icon: "success",
        showCancelButton: true,
        showDenyButton: true,
        confirmButtonColor: "#198754",
        cancelButtonColor: "#d33",
        denyButtonColor: "#3085d6",
        confirmButtonText: "Oui, Modèle 1",
        cancelButtonText: 'Non',
        denyButtonText: 'Modèle 2',
    }).then((result) => {
        if (result.isConfirmed) {
            window.open(receiptUrl + '?template=2&copies=1', '_blank');
        } else if (result.isDenied) {
            window.open(receiptUrl + '?template=1&copies=1', '_blank');
        } else {
            showToastSuccess("Modifications enregistrées!");
        }
    });
}

// Account creation success alert (status 205)
function showAccountCreationAlert() {
    return Swal.fire({
        title: 'Compte en cours de création',
        text: 'Votre compte est en cours de création. Vous recevrez un message de ECOLEPRO-CI contenant les informations de connexion dans peu de temps. Pour toute assistance technique: +225 07 59 95 14 53 (Whatsapp ou Appel)',
        icon: 'success',
        confirmButtonText: 'Compris'
    });
}

// Permission denied alert (status 403)
function showPermissionDeniedAlert() {
    return Swal.fire({
        title: 'Autorisation réfusée',
        text: "Vous n'êtes pas autorisé à voir cette page ou à éffectuer cette action.",
        icon: 'error',
        confirmButtonText: 'OK'
    });
}

// Installation success alert
function showInstallationSuccessAlert() {
    return Swal.fire({
        title: "Installation terminée",
        text: "L'application EcolePro a été installée avec succès! Pour les téléphones mobile, il peut prendre jusqu'à une minute avant d'être visible dans le Menu Applis.",
        icon: 'success',
        confirmButtonText: 'Parfait!'
    });
}

// Installation impossible alert
function showInstallationImpossibleAlert() {
    return Swal.fire({
        title: "Installation impossible",
        text: "L'application semble être déjà installée sur votre appareil. Si le problème persiste, prière contacter le : +225 07 59 95 14 53 (Appel ou Whatsapp)",
        icon: 'info',
        confirmButtonText: 'OK'
    });
}

// Generic error alert
function showErrorAlert(title = 'Erreur', text = 'Une erreur est survenue') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'error',
        confirmButtonText: 'OK'
    });
}

// Confirmation dialog
function showConfirmationDialog(options = {}) {
    const {
        title = 'Êtes-vous sûr?',
        text = 'Cette action ne peut pas être annulée.',
        confirmText = 'Oui, continuer',
        cancelText = 'Annuler',
        icon = 'warning'
    } = options;

    return Swal.fire({
        title: title,
        text: text,
        icon: icon,
        showCancelButton: true,
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        reverseButtons: true
    });
}

// Toast notifications (bottom-right corner)
function showToastSuccess(message, timer = 2000) {
    return Swal.fire({
        title: message,
        icon: "success",
        toast: true,
        showConfirmButton: false,
        timer: timer,
        position: 'bottom-end',
        showCloseButton: true
    });
}

function showToastError(message, timer = 3000) {
    return Swal.fire({
        title: message,
        icon: "error",
        toast: true,
        showConfirmButton: false,
        timer: timer,
        position: 'bottom-end',
        showCloseButton: true
    });
}

function showToastInfo(message, timer = 2000) {
    return Swal.fire({
        title: message,
        icon: "info",
        toast: true,
        showConfirmButton: false,
        timer: timer,
        position: 'bottom-end',
        showCloseButton: true
    });
}

// ===================================
// FORM ERROR HANDLING
// ===================================

// Function to dismiss form error messages
function dismissFormError() {
    const errorContainer = document.getElementById('form-error-container');
    if (errorContainer) {
        errorContainer.classList.add('fade-out');
        setTimeout(() => {
            errorContainer.remove();
        }, 300); // Match the CSS animation duration
    }
}

// Auto-dismiss form errors after 10 seconds
document.addEventListener('DOMContentLoaded', function() {
    const errorContainer = document.getElementById('form-error-container');
    if (errorContainer) {
        // Scroll to error message if it exists
        errorContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Auto-dismiss after 10 seconds
        setTimeout(() => {
            dismissFormError();
        }, 10000);
    }
});

// Function to show form error programmatically (for testing/demo purposes)
function showFormError(title, messages) {
    // Remove existing error if any
    const existingError = document.getElementById('form-error-container');
    if (existingError) {
        existingError.remove();
    }

    // Create error container
    const errorContainer = document.createElement('div');
    errorContainer.className = 'material-form-error-container';
    errorContainer.id = 'form-error-container';

    // Create messages HTML
    let messagesHtml = '';
    if (Array.isArray(messages)) {
        messages.forEach(message => {
            messagesHtml += `<div class="material-form-error-message">${message}</div>`;
        });
    } else {
        messagesHtml = `<div class="material-form-error-message">${messages}</div>`;
    }

    // Create error HTML
    errorContainer.innerHTML = `
        <div class="material-form-error">
            <div class="material-form-error-icon">
                <span class="material-icons">error_outline</span>
            </div>
            <div class="material-form-error-content">
                <div class="material-form-error-title">${title}</div>
                <div class="material-form-error-messages">
                    ${messagesHtml}
                </div>
            </div>
            <button type="button" class="material-form-error-close" onclick="dismissFormError()">
                <span class="material-icons">close</span>
            </button>
        </div>
    `;

    // Insert at the beginning of the form
    const form = document.getElementById('student-form');
    if (form) {
        form.insertBefore(errorContainer, form.firstChild);

        // Scroll to error
        errorContainer.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Auto-dismiss after 10 seconds
        setTimeout(() => {
            dismissFormError();
        }, 10000);
    }
}

// ===================================
// UTILITY FUNCTIONS
// ===================================

// Simulate loading delay for better UX
function simulateLoading(callback, delay = 300) {
    setTimeout(callback, delay);
}

// Create ripple effect for buttons
function createRipple(event, element, isCircular = false) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    if (isCircular) {
        ripple.classList.add('ripple-circular');
    }

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// ===================================
// HTMX LOADING STATES
// ===================================

// Initialize HTMX loading indicators
document.addEventListener('DOMContentLoaded', function() {
    initializeHTMXLoading();
    initializeMobileSearch();
    initializeBottomSheet();
    initializeFloatingActionButtons();
    initializeStudentCardHandlers();
});

function initializeHTMXLoading() {
    // Add loading indicators to elements that will be HTMX targets
    addLoadingIndicators();

    // Set up HTMX event listeners
    setupHTMXEventListeners();
}

function addLoadingIndicators() {
    // Add loading indicators to common HTMX target elements
    const targets = [
        '#content-area',
        '#dialog',
        '.dashboard-card',
        '.mdc-card',
        '[hx-target]'
    ];

    targets.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (!element.querySelector('.htmx-indicator')) {
                const indicator = createMaterialLoadingIndicator();
                element.appendChild(indicator);
            }
        });
    });
}

function createMaterialLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'htmx-indicator';

    indicator.innerHTML = `
        <div class="mdc-circular-progress mdc-circular-progress--htmx mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1">
            <div class="mdc-circular-progress__determinate-container">
                <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                    <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                    <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                </svg>
            </div>
            <div class="mdc-circular-progress__indeterminate-container">
                <div class="mdc-circular-progress__spinner-layer">
                    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__gap-patch">
                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                        <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    `;

    return indicator;
}

function setupHTMXEventListeners() {
    // Only set up if HTMX is available
    if (typeof htmx === 'undefined') {
        console.warn('HTMX not found, loading indicators will not work');
        return;
    }

    // Before HTMX request starts
    htmx.on('htmx:beforeRequest', function(evt) {
        const target = evt.detail.target;
        if (target) {
            target.classList.add('htmx-request');

            // Initialize Material Design progress indicator if it exists
            const indicator = target.querySelector('.mdc-circular-progress');
            if (indicator && window.mdc && window.mdc.circularProgress) {
                const progressInstance = new mdc.circularProgress.MDCCircularProgress(indicator);
                progressInstance.determinate = false;
                target._progressInstance = progressInstance;
            }
        }
    });

    // After HTMX request completes (success or error)
    htmx.on('htmx:afterRequest', function(evt) {
        const target = evt.detail.target;
        if (target) {
            target.classList.remove('htmx-request');

            // Clean up Material Design progress indicator
            if (target._progressInstance) {
                target._progressInstance.destroy();
                delete target._progressInstance;
            }
        }
    });

    // When HTMX swaps content
    htmx.on('htmx:afterSwap', function(evt) {
        const target = evt.detail.target;
        if (target) {
            // Re-add loading indicators to new content
            setTimeout(() => {
                addLoadingIndicators();
            }, 100);
        }
        initializeScrollBehavior();
    });

    // Handle HTMX errors
    htmx.on('htmx:responseError', function(evt) {
        const target = evt.detail.target;
        if (target) {
            target.classList.remove('htmx-request');

            // Clean up Material Design progress indicator
            if (target._progressInstance) {
                target._progressInstance.destroy();
                delete target._progressInstance;
            }
        }

        console.error('HTMX request failed:', evt.detail);
        showErrorAlert('Erreur de connexion', 'Une erreur est survenue lors de la communication avec le serveur.');
    });

    // Handle HTMX response status codes with SweetAlert
    htmx.on('htmx:beforeSwap', function(evt) {
        const statusCode = evt.detail.xhr.status;

        // Handle different status codes
        if (statusCode === 204) {
            // Success - show success alert
            showSuccessAlert();
            evt.detail.shouldSwap = false; // Prevent content swap for 204
        } else if (statusCode === 206) {
            // Payment success with receipt options
            try {
                const jsonResponse = JSON.parse(evt.detail.xhr.responseText);
                if (jsonResponse.receipt_url) {
                    showPaymentSuccessAlert(jsonResponse.receipt_url);
                }
            } catch (e) {
                showSuccessAlert('Enregistré!', 'Paiement enregistré avec succès');
            }
            evt.detail.shouldSwap = false; // Prevent content swap for 206
        } else if (statusCode === 205) {
            // Account creation success
            showAccountCreationAlert();
            evt.detail.shouldSwap = false; // Prevent content swap for 205
        } else if (statusCode === 403) {
            // Permission denied
            showPermissionDeniedAlert();
            evt.detail.shouldSwap = false; // Prevent content swap for 403
        } else if (statusCode === 201) {
            // Created - close modal if it exists
            const modal = document.getElementById('modal-overlay');
            if (modal && modal.classList.contains('active')) {
                hideModal();
            }
        }
    });

    // Handle modal closing for empty responses
    htmx.on('htmx:afterSwap', function(evt) {
        const target = evt.detail.target;

        // If response is empty and target is a modal, close it
        if ((target.id === 'dialog' || target.id === 'dialog-xl') && !evt.detail.xhr.response) {
            if (target.id === 'dialog') {
                hideModal();
            }
        }
    });
}

// ===================================
// MOBILE SEARCH FUNCTIONALITY
// ===================================

// Mobile search components
let mobileSearchOverlay;
let mobileSearchInput;
let mobileSearchResults;

// Mobile search click handler
function handleMobileSearchClick(e) {
    console.log('Mobile search button clicked!');
    createRipple(e, e.currentTarget, true);
    openMobileSearch();
}

// Initialize mobile search
function initializeMobileSearch() {
    console.log('Initializing mobile search...');

    mobileSearchOverlay = document.getElementById('mobile-search-overlay');
    mobileSearchInput = document.getElementById('mobile-search-input');
    mobileSearchResults = document.getElementById('mobile-search-results');

    console.log('Mobile search elements:', {
        overlay: mobileSearchOverlay,
        input: mobileSearchInput,
        results: mobileSearchResults
    });

    // Add event listeners for mobile search button
    const mobileSearchBtn = document.getElementById('mobile-search-btn');
    console.log('Mobile search button:', mobileSearchBtn);

    if (mobileSearchBtn) {
        // Remove any existing event listeners
        mobileSearchBtn.removeEventListener('click', handleMobileSearchClick);

        // Add new event listener
        mobileSearchBtn.addEventListener('click', handleMobileSearchClick);
        console.log('Mobile search button event listener added');
    } else {
        console.warn('Mobile search button not found');
    }

    // Add event listeners for back button
    const mobileSearchBack = document.getElementById('mobile-search-back');
    if (mobileSearchBack) {
        mobileSearchBack.addEventListener('click', (e) => {
            createRipple(e, mobileSearchBack, true);
            closeMobileSearch();
        });
    }

    // Handle search input
    if (mobileSearchInput) {
        mobileSearchInput.addEventListener('input', handleMobileSearch);
    }

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && mobileSearchOverlay && mobileSearchOverlay.classList.contains('active')) {
            closeMobileSearch();
        }
    });

    // Close search when clicking outside results
    if (mobileSearchOverlay) {
        mobileSearchOverlay.addEventListener('click', (e) => {
            if (e.target === mobileSearchOverlay) {
                closeMobileSearch();
            }
        });
    }
}

function openMobileSearch() {
    console.log('Opening mobile search...');
    console.log('Mobile search overlay:', mobileSearchOverlay);

    if (!mobileSearchOverlay) {
        console.error('Mobile search overlay not found!');
        return;
    }

    mobileSearchOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
    console.log('Mobile search overlay opened');

    // Focus input after animation
    setTimeout(() => {
        if (mobileSearchInput) {
            mobileSearchInput.focus();
            console.log('Mobile search input focused');
        }
    }, 300);

    // Clear previous search
    if (mobileSearchInput) {
        mobileSearchInput.value = '';
    }
    if (mobileSearchResults) {
        mobileSearchResults.innerHTML = '';
    }
}

function closeMobileSearch() {
    if (!mobileSearchOverlay) return;

    mobileSearchOverlay.classList.remove('active');
    document.body.style.overflow = '';

    if (mobileSearchInput) {
        mobileSearchInput.value = '';
    }
    if (mobileSearchResults) {
        mobileSearchResults.innerHTML = '';
    }
}

function handleMobileSearch() {
    if (!mobileSearchInput || !mobileSearchResults) return;

    const query = mobileSearchInput.value.trim().toLowerCase();

    if (query === '') {
        mobileSearchResults.innerHTML = '';
        return;
    }

    // Show loading state
    mobileSearchResults.innerHTML = `
        <div class="mobile-search-loading">
            <div class="mdc-circular-progress mdc-circular-progress--indeterminate" style="width: 32px; height: 32px;">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <span style="margin-left: 12px; color: var(--text-secondary);">Recherche en cours...</span>
        </div>
    `;

    // Perform search via HTMX
    performMobileSearch(query);
}

function performMobileSearch(query) {
    // Use HTMX to search for students
    const searchUrl = window.location.pathname + '?mobile_search=' + encodeURIComponent(query);

    fetch(searchUrl, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'HX-Request': 'true'
        }
    })
    .then(response => response.json())
    .then(data => {
        renderMobileSearchResults(data.students || [], query);
    })
    .catch(error => {
        console.error('Mobile search error:', error);
        if (mobileSearchResults) {
            mobileSearchResults.innerHTML = `
                <div class="mobile-search-no-results">
                    <span class="material-icons">error</span>
                    <div>Erreur lors de la recherche</div>
                </div>
            `;
        }
    });
}

function renderMobileSearchResults(results, query) {
    if (!mobileSearchResults) return;

    if (results.length === 0) {
        mobileSearchResults.innerHTML = `
            <div class="mobile-search-no-results">
                <span class="material-icons">search_off</span>
                <div>Aucun élève trouvé pour "${query}"</div>
            </div>
        `;
        return;
    }

    mobileSearchResults.innerHTML = results.map(student => `
        <div class="mobile-search-result-item" data-student-id="${student.id}">
            <div class="mobile-search-result-photo">
                ${student.first_name ? student.first_name.charAt(0).toUpperCase() : 'E'}
            </div>
            <div class="mobile-search-result-info">
                <div class="mobile-search-result-name">${highlightSearchTerm(student.full_name || student.first_name + ' ' + student.last_name, query)}</div>
                <div class="mobile-search-result-details">
                    ${student.level_name || 'Niveau non défini'} • ${student.matricule || student.id}
                </div>
            </div>
        </div>
    `).join('');

    // Add click handlers to search results
    mobileSearchResults.querySelectorAll('.mobile-search-result-item').forEach(item => {
        item.addEventListener('click', (e) => {
            createRipple(e, item, true);

            const studentId = item.dataset.studentId;
            const student = results.find(s => s.id == studentId);

            if (student) {
                // Close search and show bottom sheet
                closeMobileSearch();

                // Show student in bottom sheet
                const studentData = {
                    id: student.id,
                    name: student.full_name,
                    first_name: student.first_name,
                    level: student.level_name,
                    level_name: student.level_name,
                    matricule: student.matricule,
                    photo: null // Will show initials
                };

                showBottomSheet(studentData);
            }
        });
    });
}

function highlightSearchTerm(text, term) {
    if (!term || !text) return text;

    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark style="background-color: var(--primary-alpha-16); color: var(--primary-color); padding: 0;">$1</mark>');
}

// ===================================
// BOTTOM SHEET FUNCTIONALITY
// ===================================

// Bottom sheet components
let bottomSheetOverlay;
let bottomSheet;
let bottomSheetClose;
let previewPhoto;
let previewName;
let previewDetails;

// Initialize bottom sheet
function initializeBottomSheet() {
    console.log('Initializing bottom sheet...');

    bottomSheetOverlay = document.getElementById('bottom-sheet-overlay');
    bottomSheet = document.getElementById('bottom-sheet');
    bottomSheetClose = document.getElementById('bottom-sheet-close');
    previewPhoto = document.getElementById('preview-photo');
    previewName = document.getElementById('preview-name');
    previewDetails = document.getElementById('preview-details');

    console.log('Bottom sheet elements:', {
        overlay: bottomSheetOverlay,
        sheet: bottomSheet,
        close: bottomSheetClose,
        photo: previewPhoto,
        name: previewName,
        details: previewDetails
    });

    // Add event listeners for close button
    if (bottomSheetClose) {
        bottomSheetClose.addEventListener('click', (e) => {
            createRipple(e, bottomSheetClose, true);
            hideBottomSheet();
        });
    }

    // Close when clicking overlay
    if (bottomSheetOverlay) {
        bottomSheetOverlay.addEventListener('click', (e) => {
            if (e.target === bottomSheetOverlay) {
                hideBottomSheet();
            }
        });
    }

    // Add event listeners for action buttons
    const actionDetails = document.getElementById('action-details');
    const actionEdit = document.getElementById('action-edit');
    const actionPayment = document.getElementById('action-payment');
    const actionDelete = document.getElementById('action-delete');

    if (actionDetails) {
        actionDetails.addEventListener('click', (e) => {
            createRipple(e, actionDetails, true);
            const studentId = bottomSheet.dataset.studentId;
            console.log('View details for student:', studentId);
            hideBottomSheet();
            // Navigate to student detail page
            if (studentId) {
                window.location.href = `/school/students/${studentId}/`;
            }
        });
    }

    if (actionEdit) {
        actionEdit.addEventListener('click', (e) => {
            createRipple(e, actionEdit, true);
            const studentId = bottomSheet.dataset.studentId;
            console.log('Edit student:', studentId);
            hideBottomSheet();
            // TODO: Open edit modal or navigate to edit page
        });
    }

    if (actionPayment) {
        actionPayment.addEventListener('click', (e) => {
            createRipple(e, actionPayment, true);
            const studentId = bottomSheet.dataset.studentId;
            console.log('Add payment for student:', studentId);
            hideBottomSheet();
            // TODO: Open payment modal
        });
    }

    if (actionDelete) {
        actionDelete.addEventListener('click', (e) => {
            createRipple(e, actionDelete, true);
            const studentId = bottomSheet.dataset.studentId;
            const studentName = bottomSheet.dataset.studentName;
            console.log('Delete student:', studentId, studentName);
            hideBottomSheet();
            // TODO: Show confirmation dialog
        });
    }

    // Keyboard support
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && bottomSheetOverlay && bottomSheetOverlay.classList.contains('active')) {
            hideBottomSheet();
        }
    });
}

function showBottomSheet(studentData) {
    console.log('Showing bottom sheet for student:', studentData);

    if (!bottomSheetOverlay || !bottomSheet) {
        console.error('Bottom sheet elements not found!');
        return;
    }

    // Populate student preview
    if (previewPhoto) {
        if (studentData.photo && studentData.photo.startsWith('url(')) {
            previewPhoto.style.backgroundImage = studentData.photo;
        } else {
            // Show initials if no photo
            const initials = studentData.first_name ? studentData.first_name.charAt(0).toUpperCase() :
                            (studentData.name ? studentData.name.charAt(0).toUpperCase() : 'E');
            previewPhoto.textContent = initials;
            previewPhoto.style.backgroundImage = 'none';
        }
    }

    if (previewName) {
        previewName.textContent = studentData.name || studentData.full_name || 'Nom non disponible';
    }

    if (previewDetails) {
        const level = studentData.level || studentData.level_name || 'Niveau non défini';
        previewDetails.innerHTML = `<span class="student-level">${level}</span> • ${studentData.matricule || studentData.id || 'ID non disponible'}`;
    }

    // Store current student data for actions
    bottomSheet.dataset.studentId = studentData.id;
    bottomSheet.dataset.studentName = studentData.name || studentData.full_name;

    // Show bottom sheet
    bottomSheetOverlay.classList.add('active');
    bottomSheet.classList.add('active');

    console.log('Bottom sheet shown');
}

function hideBottomSheet() {
    console.log('Hiding bottom sheet...');

    if (bottomSheetOverlay) {
        bottomSheetOverlay.classList.remove('active');
    }
    if (bottomSheet) {
        bottomSheet.classList.remove('active');
    }

    console.log('Bottom sheet hidden');
}

// ===================================
// FLOATING ACTION BUTTONS
// ===================================

// Initialize floating action buttons
function initializeFloatingActionButtons() {
    console.log('Initializing floating action buttons...');

    const addStudentFab = document.getElementById('add-student-fab');
    const qrFab = document.getElementById('qr-fab');

    console.log('FAB elements:', {
        addStudentFab: addStudentFab,
        qrFab: qrFab
    });

    // Add Student FAB
    if (addStudentFab) {
        addStudentFab.addEventListener('click', (e) => {
            createRipple(e, addStudentFab, true);
            console.log('Add student FAB clicked');
            // HTMX will handle the form wizard opening
            // Show modal when HTMX loads the content
            setTimeout(() => {
                const modal = document.getElementById('modal');
                if (modal && typeof showModal === 'function') {
                    showModal();
                }
            }, 100);
        });
    }

    // QR Code FAB
    if (qrFab) {
        qrFab.addEventListener('click', (e) => {
            createRipple(e, qrFab, true);
            console.log('QR code FAB clicked');
            // TODO: Open QR scanner
            openQRScanner();
        });
    }

    // Update FAB visibility based on screen size
    updateFabVisibility();

    // Update FAB visibility on window resize
    window.addEventListener('resize', updateFabVisibility);

    // Add HTMX event listeners for FAB interactions
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Auto-show modal when form wizard content is loaded
        if (event.detail.target.id === 'modal-content' &&
            event.detail.xhr.responseURL.includes('student_add_wizard')) {
            console.log('Student form wizard loaded, showing modal');
            if (typeof showModal === 'function') {
                showModal();
            }
        }
    });
}

function updateFabVisibility() {
    const addStudentFab = document.getElementById('add-student-fab');
    const qrFab = document.getElementById('qr-fab');

    if (window.innerWidth <= 768) {
        // Show on mobile
        if (addStudentFab) addStudentFab.style.display = 'flex';
        if (qrFab) qrFab.style.display = 'flex';
    } else {
        // Hide on desktop
        if (addStudentFab) addStudentFab.style.display = 'none';
        if (qrFab) qrFab.style.display = 'none';
    }
}

function openAddStudentModal() {
    console.log('Opening add student modal...');
    // TODO: Implement add student modal
    alert('Add student functionality will be implemented here');
}

function openQRScanner() {
    console.log('Opening QR scanner...');
    // TODO: Implement QR scanner
    alert('QR scanner functionality will be implemented here');
}

// ===================================
// STUDENT CARD INTERACTIONS
// ===================================

// Initialize student card click handlers
function initializeStudentCardHandlers() {
    console.log('Initializing student card handlers...');

    // Add click handlers to student cards
    document.addEventListener('click', (e) => {
        const studentCard = e.target.closest('.clickable-student-card');
        if (studentCard) {
            e.preventDefault();

            const studentData = {
                id: studentCard.dataset.studentId,
                name: studentCard.dataset.studentName,
                level: studentCard.dataset.studentLevel,
                level_name: studentCard.dataset.studentLevel,
                matricule: studentCard.dataset.studentMatricule,
                photo: null // Will show initials
            };

            console.log('Student card clicked:', studentData);
            showBottomSheet(studentData);
        }
    });
}

// Helper function for mobile search and other components
function showStudentBottomSheet(studentId, studentName, level, matricule) {
    const studentData = {
        id: studentId,
        name: studentName,
        level: level,
        level_name: level,
        matricule: matricule,
        photo: null
    };

    showBottomSheet(studentData);
}
