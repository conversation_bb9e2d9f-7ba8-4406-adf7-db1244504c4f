{% load i18n %}
{% load matter_forms %}

<form id="modal-form"
      method="post"
      enctype="multipart/form-data"
      hx-post="{{ request.path }}"
      hx-target="#modal-content"
      hx-swap="innerHTML"
      hx-on::before-request="console.log('Submitting wizard step...')"
      hx-on::after-request="console.log('Wizard step response received', event.detail.xhr.status)">
    {% csrf_token %}
    {{ wizard.management_form }}

    <!-- Form Error Messages -->
    {% if wizard.form.non_field_errors %}
        <div class="material-form-error-container" id="form-error-container">
            <div class="material-form-error">
                <div class="material-form-error-icon">
                    <span class="material-icons">error_outline</span>
                </div>
                <div class="material-form-error-content">
                    <div class="material-form-error-title">Erreur de validation</div>
                    <div class="material-form-error-messages">
                        {% for error in wizard.form.non_field_errors %}
                            <div class="material-form-error-message">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                <button type="button" class="material-form-error-close" onclick="dismissFormError()">
                    <span class="material-icons">close</span>
                </button>
            </div>
        </div>
    {% endif %}

    <!-- Step Progress Indicator -->
    <div class="wizard-progress-container d-none">
        <!-- <div class="wizard-progress-header">
            <div class="wizard-progress-title">
                <span class="material-icons">person_add</span>
                Inscription d'un élève
            </div>
            <div class="wizard-progress-subtitle">
                Étape {{ wizard.steps.step1 }} sur {{ wizard.steps.count }}
            </div>
        </div> -->
        
        <div class="wizard-progress-steps">
            <div class="wizard-step {% if wizard.steps.step1 == 1 %}active{% elif wizard.steps.step1 > 1 %}completed{% endif %}">
                <div class="wizard-step-circle">
                    {% if wizard.steps.step1 > 1 %}
                        <span class="material-icons">check</span>
                    {% else %}
                        <span>1</span>
                    {% endif %}
                </div>
                <div class="wizard-step-label">Informations élève</div>
            </div>
            
            <div class="wizard-step-connector {% if wizard.steps.step1 > 1 %}completed{% endif %}"></div>
            
            <div class="wizard-step {% if wizard.steps.step1 == 2 %}active{% elif wizard.steps.step1 > 2 %}completed{% endif %}">
                <div class="wizard-step-circle">
                    {% if wizard.steps.step1 > 2 %}
                        <span class="material-icons">check</span>
                    {% else %}
                        <span>2</span>
                    {% endif %}
                </div>
                <div class="wizard-step-label">Parent & Scolarité</div>
            </div>
        </div>
    </div>

    <!-- Step Content -->
    <div class="wizard-step-content">
        {% if wizard.steps.step1 == 1 %}
            <!-- Step 1: Student Information -->
            <!-- <div class="form-section-title">
                <span class="material-icons">person</span>
                Informations sur l'élève
            </div> -->

            <!-- Student ID -->
            <div class="row">
                <div class="form-field">
                    {% matter_field wizard.form.student_id class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                </div>
            </div>

            <!-- Name Fields -->
            <div class="row gx-2">
                <div class="form-field">
                    {% matter_field wizard.form.last_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                </div>
            </div>
            <div class="row gx-2">
                <div class="form-field">
                    {% if user.school.education != 'F' %}
                        {% matter_field wizard.form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=full_name_ar" hx_target="#id_full_name_ar_container" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field wizard.form.first_name class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
                
            </div>
            <div class="row gx-2">
                <div class="form-field col-4">
                    {% matter_field wizard.form.gender class="small" %}
                </div>
                <div class="form-field col-8">
                    {% matter_field wizard.form.nationality class="small" %}
                </div>
            </div>

            <!-- Birth Date Fields -->
            <div class="row gx-2">
                <div class="form-field col-sm-4">
                    {% matter_field wizard.form.birth_day class="small" type="number" min="1" max="31" %}
                </div>
                <div class="form-field col-sm-4">
                    {% matter_field wizard.form.birth_month class="small" %}
                </div>
                <div class="form-field col-sm-4">
                    {% matter_field wizard.form.birth_year class="small" type="number" max="2024" %}
                </div>
            </div>

            <!-- Birth Place, Gender, Nationality -->
            <div class="row gx-2">
                <div class="form-field col-6">
                    {% if user.school.education != 'F' %}
                        {% matter_field wizard.form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" hx_get="/transliterate/?field=birth_place_ar" hx_target="#id_birth_place_ar_container" hx_trigger="keyup delay:3s" hx_swap="outerHTML" %}
                    {% else %}
                        {% matter_field wizard.form.birth_place class="small" onkeyup="this.value = this.value.toUpperCase();" %}
                    {% endif %}
                </div>
                <div class="form-field col-6">
                    {% matter_field wizard.form.father_phone class="small" type="tel" %}
                </div>
            </div>

            <!-- Arabic Name Fields (if not French education) -->
            {% if user.school.education != 'F' %}
            <div class="row gx-2">
                <div class="form-field col-6">
                    {% matter_field wizard.form.full_name_ar class='small' %}
                </div>
                <div class="form-field col-6">
                    {% matter_field wizard.form.birth_place_ar class='small' %}
                </div>
            </div>
            {% endif %}

            <!-- Photo Upload Section -->
            <div class="photo-upload-section">
                <div class="photo-upload-title">
                    <span class="material-icons">photo_camera</span>
                    Photo de l'élève
                </div>
                <img alt="Photo de l'élève" class="photo-preview"
                     src="/static/img/avatar.jpg" id="photo-preview">

                <input type="file" class="mdc-text-field__input"
                       name="0-photo" id="{{ wizard.form.photo.id_for_label }}"
                       accept=".jpg, .png, .jpeg"
                       onchange="previewPhoto(this)"
                       style="display: none;">

                <button type="button" class="mdc-button mdc-button--outlined"
                        onclick="document.getElementById('{{ wizard.form.photo.id_for_label }}').click()">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons mdc-button__icon">photo_camera</span>
                    <span class="mdc-button__label">Choisir une photo</span>
                </button>
            </div>
            
        {% elif wizard.steps.step1 == 2 %}
            <!-- Step 2: Parent & School Information -->
            <div class="wizard-step-sections">
                <!-- Parent Information Section -->
                <div class="form-section-title">
                    <span class="material-icons">family_restroom</span>
                    Informations parent/tuteur
                </div>

                <!-- Father -->
                <div class="form-field">
                    {% matter_field wizard.form.father class='small' onkeyup="this.value = this.value.toUpperCase();" %}
                </div>

                <!-- Mother -->
                <div class="form-field">
                    {% matter_field wizard.form.mother class='small' onkeyup="this.value = this.value.toUpperCase();" %}
                </div>

                <!-- Parent Phone -->
                <div class="form-field">
                    {% matter_field wizard.form.father_phone class='small' type="tel" %}
                </div>

                <!-- School Information Section -->
                <div class="form-section-title" style="margin-top: 24px;">
                    <span class="material-icons">school</span>
                    Informations scolaires
                </div>

                <!-- Subschool (if multiple) -->
                {% if user.school.subschool_set.count > 1 %}
                <div class="form-field">
                    {% matter_field wizard.form.subschool class='small' %}
                </div>
                {% endif %}

                <!-- Level Fields -->
                <div class="row gx-2">
                    {% if wizard.form.level_fr.help_text != 'd-none' %}
                    <div class="form-field col-md-6">
                        {% matter_field wizard.form.level_fr class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                        {% matter_field wizard.form.generic_level_fr class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    </div>
                    {% endif %}

                    {% if wizard.form.level_ar.help_text != 'd-none' %}
                    <div class="form-field col-md-6">
                        {% matter_field wizard.form.level_ar class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                        {% matter_field wizard.form.generic_level_ar class='small d-none' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    </div>
                    {% endif %}
                </div>

                <!-- Quality and Status -->
                <div class="row gx-2">
                    <div class="form-field col-md-6" style="flex: 2;">
                        {% matter_field wizard.form.qualite class='small' %}
                    </div>
                    <div class="form-field col-md-6" style="flex: 1;">
                        {% matter_field wizard.form.status class='small' hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees_container" hx_swap="outerHTML" %}
                    </div>
                </div>

                <!-- Fees Section -->
                {% if perms.school.add_payment %}
                <div class="row gx-2">
                    <div class="form-field col-md-4">
                        {% matter_field wizard.form.enrollment_fees class='small' type="number" min="0" %}
                    </div>
                    <div class="form-field col-md-4">
                        {% matter_field wizard.form.year_fees class='small' type="number" min="0" hx_swap="outerHTML" %}
                    </div>
                    <div class="form-field col-md-4">
                        {% matter_field wizard.form.annexe_fees class='small' type="number" min="0" %}
                    </div>
                </div>

                <!-- Compact Payment Section -->
                <div class="form-section-title" style="margin-top: 16px;">
                    <span class="material-icons">receipt_long</span>
                    Premier versement (optionnel)
                </div>

                <div class="row gx-2">
                    <div class="form-field col-md-3">
                        {% matter_field wizard.form.enrollment_fee1 class='extra-small' type="number" min="0" placeholder="0" %}
                    </div>
                    <div class="form-field col-md-3">
                        {% matter_field wizard.form.year_fee1 class='extra-small' type="number" min="0" placeholder="0" %}
                    </div>
                    <div class="form-field col-md-3">
                        {% matter_field wizard.form.annexe_fee1 class='extra-small' type="number" min="0" placeholder="0" %}
                    </div>
                    <div class="form-field col-md-3">
                        {% matter_field wizard.form.date1 class='extra-small' type="date" %}
                    </div>
                </div>
                {% endif %}
            </div>
        {% endif %}
    </div>

    <!-- Form Actions -->
    <div id="modal-actions" class="modal-actions" hx-swap-oob="true">
        {% if wizard.steps.prev %}
            <button type="submit" 
                    name="wizard_goto_step" 
                    form="modal-form"
                    value="{{ wizard.steps.prev }}" 
                    class="mdc-button mdc-button--outlined wizard-prev-btn">
                <span class="material-icons mdc-button__icon">arrow_back</span>
                <span class="mdc-button__label">Précédent</span>
            </button>
        {% endif %}

        <div class="wizard-actions-spacer"></div>

        {% if wizard.steps.next %}
            <button type="submit" 
                    form="modal-form"
                    class="mdc-button mdc-button--raised wizard-next-btn">
                <span class="mdc-button__label">Suivant</span>
                <span class="material-icons mdc-button__icon">arrow_forward</span>
            </button>
        {% else %}
            <button type="submit"
                    form="modal-form" 
                    class="mdc-button mdc-button--raised wizard-submit-btn">
                <span class="material-icons mdc-button__icon">check</span>
                <span class="mdc-button__label">Terminer</span>
            </button>
        {% endif %}
    </div>
</form>

<script>
// Initialize form components when wizard loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Matter CSS components
    if (typeof initializeMatterComponents === 'function') {
        initializeMatterComponents();
    }
    
    // Initialize date fields
    if (typeof initializeDateFields === 'function') {
        initializeDateFields();
    }
    
    // Add ripple effects to wizard buttons
    document.querySelectorAll('.wizard-prev-btn, .wizard-next-btn, .wizard-submit-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            if (typeof createRipple === 'function') {
                createRipple(e, btn);
            }
        });
    });
});

// Also initialize when HTMX swaps content
document.body.addEventListener('htmx:afterSwap', function(event) {
    if (event.detail.target.id === 'modal-content') {
        // Re-initialize components after HTMX swap
        if (typeof initializeMatterComponents === 'function') {
            initializeMatterComponents();
        }

        if (typeof initializeDateFields === 'function') {
            initializeDateFields();
        }
    }
});

// Handle form submission success
document.body.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.xhr.status === 200 && event.detail.target.id === 'modal-content') {
        // Check if the response indicates success (no more wizard steps)
        const responseText = event.detail.xhr.responseText;
        if (!responseText.includes('wizard-progress-container')) {
            // Form completed successfully, close modal and refresh page
            console.log('Student form wizard completed successfully');
            if (typeof hideModal === 'function') {
                hideModal();
            }
            // Refresh the students list
            window.location.reload();
        }
    }
});

// Photo preview function
function previewPhoto(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('photo-preview');
            if (preview) {
                preview.src = e.target.result;
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
