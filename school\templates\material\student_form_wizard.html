{% load i18n %}
{% load matter_forms %}

<form id="modal-form" 
      method="post"
      enctype="multipart/form-data"
      hx-post="{{ request.path }}"
      hx-target="#modal-content">
    {% csrf_token %}
    {{ wizard.management_form }}

    <!-- Form Error Messages -->
    {% if wizard.form.non_field_errors %}
        <div class="material-form-error-container" id="form-error-container">
            <div class="material-form-error">
                <div class="material-form-error-icon">
                    <span class="material-icons">error_outline</span>
                </div>
                <div class="material-form-error-content">
                    <div class="material-form-error-title">Erreur de validation</div>
                    <div class="material-form-error-messages">
                        {% for error in wizard.form.non_field_errors %}
                            <div class="material-form-error-message">{{ error }}</div>
                        {% endfor %}
                    </div>
                </div>
                <button type="button" class="material-form-error-close" onclick="dismissFormError()">
                    <span class="material-icons">close</span>
                </button>
            </div>
        </div>
    {% endif %}

    <!-- Step Progress Indicator -->
    <div class="wizard-progress-container">
        <div class="wizard-progress-header">
            <div class="wizard-progress-title">
                <span class="material-icons">person_add</span>
                Inscription d'un élève
            </div>
            <div class="wizard-progress-subtitle">
                Étape {{ wizard.steps.step1 }} sur {{ wizard.steps.count }}
            </div>
        </div>
        
        <div class="wizard-progress-steps">
            <div class="wizard-step {% if wizard.steps.step1 == 1 %}active{% elif wizard.steps.step1 > 1 %}completed{% endif %}">
                <div class="wizard-step-circle">
                    {% if wizard.steps.step1 > 1 %}
                        <span class="material-icons">check</span>
                    {% else %}
                        <span>1</span>
                    {% endif %}
                </div>
                <div class="wizard-step-label">Informations élève</div>
            </div>
            
            <div class="wizard-step-connector {% if wizard.steps.step1 > 1 %}completed{% endif %}"></div>
            
            <div class="wizard-step {% if wizard.steps.step1 == 2 %}active{% elif wizard.steps.step1 > 2 %}completed{% endif %}">
                <div class="wizard-step-circle">
                    {% if wizard.steps.step1 > 2 %}
                        <span class="material-icons">check</span>
                    {% else %}
                        <span>2</span>
                    {% endif %}
                </div>
                <div class="wizard-step-label">Parent & Scolarité</div>
            </div>
        </div>
    </div>

    <!-- Step Content -->
    <div class="wizard-step-content">
        {% if wizard.steps.step1 == 1 %}
            <!-- Step 1: Student Information -->
            <div class="form-section-title">
                <span class="material-icons">person</span>
                Informations sur l'élève
            </div>
            
            <div class="wizard-form-grid">
                {% for field in wizard.form %}
                    {% if field.name == 'first_name' or field.name == 'last_name' %}
                        <div class="form-field mdc-col-6">
                            {% matter_field field %}
                        </div>
                    {% elif field.name == 'first_name_ar' or field.name == 'last_name_ar' %}
                        <div class="form-field mdc-col-6">
                            {% matter_field field %}
                        </div>
                    {% elif field.name == 'birth_date' or field.name == 'birth_place' %}
                        <div class="form-field mdc-col-6">
                            {% matter_field field %}
                        </div>
                    {% elif field.name == 'gender' %}
                        <div class="form-field mdc-col-6">
                            {% matter_field field %}
                        </div>
                    {% elif field.name == 'student_id' %}
                        <div class="form-field mdc-col-6">
                            {% matter_field field %}
                        </div>
                    {% elif field.name == 'photo' %}
                        <div class="form-field mdc-col-12">
                            <div class="photo-upload-section">
                                <div class="photo-upload-title">
                                    <span class="material-icons">photo_camera</span>
                                    Photo de l'élève
                                </div>
                                {% matter_field field %}
                            </div>
                        </div>
                    {% else %}
                        <div class="form-field mdc-col-12">
                            {% matter_field field %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
            
        {% elif wizard.steps.step1 == 2 %}
            <!-- Step 2: Parent & School Information -->
            <div class="wizard-step-sections">
                <!-- Parent Information Section -->
                <div class="form-section-title">
                    <span class="material-icons">family_restroom</span>
                    Informations parent/tuteur
                </div>
                
                <div class="wizard-form-grid">
                    {% for field in wizard.form %}
                        {% if 'father' in field.name or 'mother' in field.name or 'parent' in field.name %}
                            {% if field.name == 'father_phone' or field.name == 'mother_phone' %}
                                <div class="form-field mdc-col-6">
                                    {% matter_field field %}
                                </div>
                            {% else %}
                                <div class="form-field mdc-col-12">
                                    {% matter_field field %}
                                </div>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                </div>

                <!-- School Information Section -->
                <div class="form-section-title">
                    <span class="material-icons">school</span>
                    Informations scolaires
                </div>
                
                <div class="wizard-form-grid">
                    {% for field in wizard.form %}
                        {% if 'level' in field.name or 'fees' in field.name or field.name == 'status' %}
                            {% if 'fees' in field.name %}
                                <div class="form-field mdc-col-4">
                                    {% if field.name == 'level_fr' or field.name == 'level_ar' %}
                                        {% matter_field field hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees" hx_swap="outerHTML" %}
                                    {% else %}
                                        {% matter_field field %}
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="form-field mdc-col-6">
                                    {% if field.name == 'level_fr' or field.name == 'level_ar' %}
                                        {% matter_field field hx_get="/versements/frais_scolarite/" hx_target="#id_2-year_fees" hx_swap="outerHTML" %}
                                    {% else %}
                                        {% matter_field field %}
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Form Actions -->
    <div class="wizard-form-actions">
        {% if wizard.steps.prev %}
            <button type="submit" 
                    name="wizard_goto_step" 
                    value="{{ wizard.steps.prev }}" 
                    class="mdc-button mdc-button--outlined wizard-prev-btn">
                <span class="material-icons mdc-button__icon">arrow_back</span>
                <span class="mdc-button__label">Précédent</span>
            </button>
        {% endif %}

        <div class="wizard-actions-spacer"></div>

        {% if wizard.steps.next %}
            <button type="submit" 
                    class="mdc-button mdc-button--raised wizard-next-btn">
                <span class="mdc-button__label">Suivant</span>
                <span class="material-icons mdc-button__icon">arrow_forward</span>
            </button>
        {% else %}
            <button type="submit" 
                    class="mdc-button mdc-button--raised wizard-submit-btn">
                <span class="material-icons mdc-button__icon">check</span>
                <span class="mdc-button__label">Terminer l'inscription</span>
            </button>
        {% endif %}
    </div>
</form>

<script>
// Initialize form components when wizard loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Matter CSS components
    if (typeof initializeMatterComponents === 'function') {
        initializeMatterComponents();
    }
    
    // Initialize date fields
    if (typeof initializeDateFields === 'function') {
        initializeDateFields();
    }
    
    // Add ripple effects to wizard buttons
    document.querySelectorAll('.wizard-prev-btn, .wizard-next-btn, .wizard-submit-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            if (typeof createRipple === 'function') {
                createRipple(e, btn);
            }
        });
    });
});

// Also initialize when HTMX swaps content
document.body.addEventListener('htmx:afterSwap', function(event) {
    if (event.detail.target.id === 'modal-content') {
        // Re-initialize components after HTMX swap
        if (typeof initializeMatterComponents === 'function') {
            initializeMatterComponents();
        }

        if (typeof initializeDateFields === 'function') {
            initializeDateFields();
        }
    }
});

// Handle form submission success
document.body.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.xhr.status === 200 && event.detail.target.id === 'modal-content') {
        // Check if the response indicates success (no more wizard steps)
        const responseText = event.detail.xhr.responseText;
        if (!responseText.includes('wizard-progress-container')) {
            // Form completed successfully, close modal and refresh page
            console.log('Student form wizard completed successfully');
            if (typeof hideModal === 'function') {
                hideModal();
            }
            // Refresh the students list
            window.location.reload();
        }
    }
});
</script>
